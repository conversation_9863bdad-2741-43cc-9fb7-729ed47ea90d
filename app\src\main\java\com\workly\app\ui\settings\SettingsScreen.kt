package com.workly.app.ui.settings

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import com.workly.app.R
import com.workly.app.ui.settings.components.*
import com.workly.app.ui.viewmodel.MainViewModel
import com.workly.app.ui.viewmodel.SettingsViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    mainViewModel: MainViewModel
) {
    val application = androidx.compose.ui.platform.LocalContext.current.applicationContext as com.workly.app.WorklyApplication
    val viewModel: SettingsViewModel = viewModel(
        factory = ViewModelFactory(application)
    )

    val settings by viewModel.settings.collectAsState()
    val isLoading by viewModel.isLoading.collectAsState()
    val error by viewModel.error.collectAsState()
    val successMessage by viewModel.successMessage.collectAsState()

    // Show error snackbar
    error?.let { errorMessage ->
        LaunchedEffect(errorMessage) {
            // TODO: Show snackbar
            viewModel.clearError()
        }
    }

    // Show success snackbar
    successMessage?.let { message ->
        LaunchedEffect(message) {
            // TODO: Show snackbar
            viewModel.clearSuccessMessage()
        }
    }

    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // Top bar
        TopAppBar(
            title = { Text(stringResource(R.string.settings)) },
            navigationIcon = {
                IconButton(onClick = { navController.popBackStack() }) {
                    Icon(Icons.Default.ArrowBack, contentDescription = "Back")
                }
            }
        )

        if (isLoading) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator()
            }
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                item {
                    // Quick Actions
                    QuickActionsSection(navController)
                }

                item {
                    // General Settings
                    GeneralSettingsSection(
                        settings = settings,
                        onLanguageChange = viewModel::updateLanguage,
                        onThemeChange = viewModel::updateTheme,
                        onMultiButtonModeChange = viewModel::updateMultiButtonMode,
                        onTimeFormatChange = viewModel::updateTimeFormat,
                        onFirstDayOfWeekChange = viewModel::updateFirstDayOfWeek
                    )
                }

                item {
                    // Alarm Settings
                    AlarmSettingsSection(
                        settings = settings,
                        onAlarmSettingsChange = viewModel::updateAlarmSettings
                    )
                }

                item {
                    // Weather Settings
                    WeatherSettingsSection(
                        settings = settings,
                        onWeatherSettingsChange = viewModel::updateWeatherSettings
                    )
                }

                item {
                    // Shift Reminder Settings
                    ShiftReminderSettingsSection(
                        settings = settings,
                        onShiftReminderModeChange = viewModel::updateShiftReminderMode
                    )
                }

                item {
                    // Data Management
                    DataManagementSection(
                        onExportData = viewModel::exportData,
                        onImportData = viewModel::importData,
                        onResetSettings = viewModel::resetToDefaults,
                        onClearAllData = viewModel::clearAllData
                    )
                }

                item {
                    // App Information
                    AppInformationSection()
                }
            }
        }
    }
}
