package com.workly.app.data.model

import kotlinx.serialization.Serializable

@Serializable
data class UserSettings(
    val language: String = "vi", // 'vi', 'en'
    val theme: String = "system", // 'light', 'dark', 'system'
    val multiButtonMode: String = "full", // 'full', 'simple'
    val alarmSoundEnabled: Boolean = true,
    val alarmVibrationEnabled: Boolean = true,
    val weatherWarningEnabled: Boolean = true,
    val weatherLocation: WeatherLocation? = null,
    val changeShiftReminderMode: String = "ask_weekly", // 'ask_weekly', 'rotate', 'disabled'
    val timeFormat: String = "24h", // '12h', '24h'
    val firstDayOfWeek: String = "Mon", // 'Mon', 'Sun'
    val notesDisplayLimit: Int = 3, // Number of notes to display in home screen (2, 3, 5)
    val createdAt: String = "",
    val updatedAt: String = ""
)

@Serializable
data class WeatherLocation(
    val lat: Double? = null,
    val lon: Double? = null,
    val cityName: String? = null,
    val countryCode: String? = null
)
