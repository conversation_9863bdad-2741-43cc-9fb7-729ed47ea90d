package com.workly.app.ui

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navArgument
import com.workly.app.ui.home.HomeScreen
import com.workly.app.ui.settings.SettingsScreen
import com.workly.app.ui.shifts.ShiftManagementScreen
import com.workly.app.ui.shifts.ShiftTemplatesScreen
import com.workly.app.ui.shifts.AddEditShiftScreen
import com.workly.app.ui.notes.NotesScreen
import com.workly.app.ui.notes.AddEditNoteScreen
import com.workly.app.ui.statistics.StatisticsScreen
import com.workly.app.ui.theme.WorklyTheme
import com.workly.app.ui.viewmodel.MainViewModel
import com.workly.app.ui.viewmodel.ViewModelFactory

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        enableEdgeToEdge()
        
        setContent {
            val mainViewModel: MainViewModel = viewModel(
                factory = ViewModelFactory(application as com.workly.app.WorklyApplication)
            )
            
            val isDarkTheme by mainViewModel.isDarkTheme.collectAsState()
            
            WorklyTheme(darkTheme = isDarkTheme) {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    WorklyApp(mainViewModel = mainViewModel)
                }
            }
        }
    }
}

@Composable
fun WorklyApp(mainViewModel: MainViewModel) {
    val navController = rememberNavController()
    
    Scaffold(
        modifier = Modifier.fillMaxSize()
    ) { innerPadding ->
        NavHost(
            navController = navController,
            startDestination = "home",
            modifier = Modifier.padding(innerPadding)
        ) {
            composable("home") {
                HomeScreen(
                    navController = navController,
                    mainViewModel = mainViewModel
                )
            }
            
            composable("settings") {
                SettingsScreen(
                    navController = navController,
                    mainViewModel = mainViewModel
                )
            }
            
            composable(
                route = "shifts?mode={mode}",
                arguments = listOf(navArgument("mode") {
                    type = NavType.StringType
                    defaultValue = "normal"
                })
            ) { backStackEntry ->
                val mode = backStackEntry.arguments?.getString("mode") ?: "normal"
                ShiftManagementScreen(
                    navController = navController,
                    mainViewModel = mainViewModel,
                    isRotationMode = mode == "rotation"
                )
            }

            composable("shift_templates") {
                ShiftTemplatesScreen(
                    navController = navController
                )
            }

            composable("add_shift") {
                AddEditShiftScreen(
                    navController = navController
                )
            }

            composable("edit_shift/{shiftId}") { backStackEntry ->
                val shiftId = backStackEntry.arguments?.getString("shiftId")
                AddEditShiftScreen(
                    navController = navController,
                    shiftId = shiftId
                )
            }

            composable("notes") {
                NotesScreen(
                    navController = navController,
                    mainViewModel = mainViewModel
                )
            }

            composable("add_note") {
                AddEditNoteScreen(
                    navController = navController
                )
            }

            composable("edit_note/{noteId}") { backStackEntry ->
                val noteId = backStackEntry.arguments?.getString("noteId")
                AddEditNoteScreen(
                    navController = navController,
                    noteId = noteId
                )
            }

            composable("statistics") {
                StatisticsScreen(
                    navController = navController,
                    mainViewModel = mainViewModel
                )
            }
        }
    }
}
